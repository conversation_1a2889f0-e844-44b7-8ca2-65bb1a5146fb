# 🚀 QuickTrack - Friction-Free Time Tracking for Freelancers

## What is QuickTrack?

QuickTrack is a **zero-friction time tracking web app** designed specifically for freelancers who hate dealing with complicated time tracking software. It eliminates every pain point:

✅ **No accounts or logins** - works instantly  
✅ **Auto-detects project names** from browser tabs  
✅ **Smart idle detection** - pauses when you're away  
✅ **One-click billable/non-billable toggle**  
✅ **Perfect CSV export** for invoice integration  
✅ **Client rate management** with automatic calculations  

## 🎯 Quick Start (2 Minutes)

### Step 1: Open QuickTrack
1. **Double-click** `quicktrack.html` to open in your browser
2. **Bookmark it** (Ctrl+D) for instant access
3. **Pin the tab** so it's always available

### Step 2: Start Tracking
1. **Type your project name** (or let it auto-detect from your browser tab)
2. **Press Space or click Start**
3. **Work normally** - QuickTrack runs in the background

### Step 3: Export for Invoicing
1. **Click "📄 Export & Filter"**
2. **Select date range and client**
3. **Include rates & totals** (checkbox)
4. **Import CSV into your invoice system**

**That's it!** You're now tracking time like a pro.

---

## 📋 Complete Feature Guide

### ⏱️ **Time Tracking**

**Starting a Timer:**
- **Space bar** = instant start/stop (works anywhere in the app)
- **Auto-detection** = project names pulled from browser tab titles
- **Manual entry** = type any project name

**Smart Features:**
- **Idle detection** = auto-pauses after 5 minutes of inactivity
- **Auto-resume** = continues when you return to work
- **Floating timer** = always visible in top-right corner
- **Tab title updates** = shows current project while tracking

### 💰 **Client Rate Management**

**Setting Rates:**
1. **Click "💰 Client Rates"**
2. **Enter client name and hourly rate**
3. **Rates are saved permanently** (stored locally)

**Using Rates:**
- **Automatic calculations** in CSV exports
- **Per-client rates** (e.g., "Client A: $75/hr", "Client B: $50/hr")
- **No manual math** required

### ⚡ **Billable vs Non-Billable**

**Toggle Billable Status:**
- **Click "Bill" or "Non-Bill"** on any session
- **Green = Billable**, **Red = Non-Billable**
- **Perfect for separating** client work from admin tasks

**Default Behavior:**
- **New sessions default to billable**
- **Manual entries can be set** during creation
- **Easy to change** anytime after tracking

### 📊 **Views & Organization**

**Daily View:**
- **Grouped by date** with daily totals
- **Recent tasks** for quick project restart
- **Full session details** with all controls

**Weekly View:**
- **7-day overview** with navigation arrows
- **Weekly statistics** (total time, sessions, projects, averages)
- **Day-by-day breakdown**
- **Bulk operations** (select multiple sessions)

### 📄 **Export & Integration**

**Smart Export Options:**
- **Date ranges** (defaults to last 30 days)
- **Specific clients** (filter by project)
- **Billable status** (all, billable only, non-billable only)
- **Include rates & totals** (automatic calculations)

**CSV Output Includes:**
```
Project, Date, Start Time, End Time, Duration (Hours), Duration (Minutes), 
Billable, Hourly Rate, Total Amount
```

**Perfect for Invoice Systems:**
- **All calculations done** automatically
- **Ready to import** into any invoice software
- **Professional formatting**

### ➕ **Manual Time Entry**

**When to Use:**
- **Forgot to start timer**
- **Backdating sessions**
- **Adding phone calls or meetings**

**How to Add:**
1. **Click "➕ Add Manual Time"**
2. **Enter project, duration, and date**
3. **Set billable status**
4. **Session appears** in your timeline

### 🔧 **Bulk Operations**

**Managing Multiple Sessions:**
1. **Switch to Weekly view**
2. **Click "Select Multiple"**
3. **Check sessions** you want to manage
4. **Bulk delete or export** selected sessions

**Great for:**
- **Cleaning up test data**
- **Exporting specific client work**
- **Managing old sessions**

---

## 🔗 Integration with Your Invoice System

### Perfect Workflow:
1. **Track time** in QuickTrack throughout the week
2. **Set client rates** once per client
3. **Export filtered CSV** for specific clients/date ranges
4. **Import CSV** into your invoice generator
5. **All calculations already done** - just generate invoice!

### Export Tips:
- **Use date filters** to match your billing periods
- **Filter by client** for individual invoices
- **Include rates & totals** for automatic calculations
- **Export billable only** to exclude admin time

---

## ⌨️ Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| **Space** | Start/Stop timer |
| **Ctrl+E** | Open export modal |
| **Escape** | Close modals or focus project input |
| **Enter** | Save in any modal |

---

## 📱 Mobile Use

QuickTrack works perfectly on phones and tablets:
- **Responsive design** adapts to any screen size
- **Touch-friendly buttons** for easy tapping
- **All features available** on mobile
- **Bookmark to home screen** for app-like experience

---

## 💾 Data Storage

**Local Storage Only:**
- **All data stays on your device** - completely private
- **No accounts or cloud sync** required
- **Works offline** completely
- **Your data, your control**

**Backup Tip:**
Your data is stored in browser local storage. To backup:
1. **Export all sessions** to CSV
2. **Save the CSV file** as your backup
3. **Re-import manually** if needed (by adding manual entries)

---

## 🛠️ Troubleshooting

### **Timer not starting?**
- Make sure you're not in an input field when pressing Space
- Try clicking the Start button directly
- Refresh the page if needed

### **Data disappeared?**
- Check if you're using the same browser
- Browser data might have been cleared
- Import your backup CSV if available

### **Export not working?**
- Make sure you have sessions to export
- Check your browser's download settings
- Try a different browser if issues persist

### **Mobile layout issues?**
- Try refreshing the page
- Ensure you're using a modern browser
- Zoom out if elements seem cramped

---

## 🎯 Pro Tips for Maximum Efficiency

### **Setup Tips:**
1. **Bookmark QuickTrack** for instant access
2. **Set all client rates** upfront
3. **Pin the browser tab** so it's always visible
4. **Use project naming conventions** (e.g., "Client - Task")

### **Daily Workflow:**
1. **Start timer before switching tasks**
2. **Let auto-detection grab project names** from tabs
3. **Toggle non-billable** for admin time
4. **Use Recent Tasks** for quick restarts

### **Weekly Workflow:**
1. **Switch to Weekly view** on Fridays
2. **Review and clean up** any mis-categorized sessions
3. **Export by client** for invoicing
4. **Use bulk operations** to manage old data

### **Integration Tips:**
1. **Export with rates included** for automatic calculations
2. **Filter by billable only** for client invoices
3. **Use consistent project names** for easier filtering
4. **Export regularly** to avoid large datasets

---

## 🆘 Need Help?

**Common Questions:**

**Q: Can I sync between devices?**
A: QuickTrack stores data locally for privacy. Use CSV export/import to transfer data between devices.

**Q: What if I close the browser while timing?**
A: QuickTrack warns you before closing with an active timer. Your data is auto-saved every 10 seconds.

**Q: Can I edit past sessions?**
A: Yes! Click the ✏️ edit button on any session to rename it, or click the billable toggle to change status.

**Q: How do I delete old data?**
A: Use bulk selection in Weekly view to select and delete multiple sessions at once.

**Q: Does it work offline?**
A: Yes! QuickTrack works completely offline. No internet connection required.

---

## 🎉 You're All Set!

QuickTrack is designed to **get out of your way** and let you focus on your actual work. The less you think about time tracking, the better it's working.

**Remember:** The goal is to track time so accurately and effortlessly that you never lose billable hours again. Happy tracking! 

---

*QuickTrack - Because time tracking shouldn't be work.*