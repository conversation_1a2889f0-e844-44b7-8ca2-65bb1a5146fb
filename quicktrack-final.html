<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuickTrack Final - Complete Invoice Integration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, 
#667eea 0%, 
#764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 90%;
            max-width: 700px;
            text-align: center;
        }

        h1 {
            color: 
#4a5568;
            margin-bottom: 0.5rem;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .subtitle {
            color: 
#718096;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        .timer-display {
            font-size: 4rem;
            font-weight: 300;
            color: 
#2d3748;
            margin: 2rem 0;
            font-family: 'Courier New', monospace;
            background: 
#f7fafc;
            padding: 1rem;
            border-radius: 15px;
            border: 2px solid 
#e2e8f0;
        }

        .timer-display.running {
            background: linear-gradient(135deg, 
#48bb78, 
#38a169);
            color: white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        .project-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid 
#e2e8f0;
            border-radius: 10px;
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
            background: 
#f7fafc;
            transition: all 0.3s ease;
            position: relative;
        }

        .project-input:focus {
            outline: none;
            border-color: 
#667eea;
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .project-input.has-rate {
            border-color: 
#48bb78;
            background: 
#f0fff4;
        }

        .project-input.has-rate:focus {
            border-color: 
#38a169;
        }

        .rate-indicator {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: 
#48bb78;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            pointer-events: none;
        }

        .client-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid 
#e2e8f0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 100;
            max-height: 200px;
            overflow-y: auto;
        }

        .client-suggestion {
            padding: 0.8rem;
            cursor: pointer;
            border-bottom: 1px solid 
#f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .client-suggestion:hover,
        .client-suggestion.active {
            background: 
#f7fafc;
        }

        .client-suggestion.active {
            background: 
#e6fffa;
            border-left: 3px solid 
#38a169;
        }

        .client-suggestion:last-child {
            border-bottom: none;
        }

        .suggestion-name {
            font-weight: 600;
            color: 
#2d3748;
        }

        .suggestion-rate {
            font-family: 'Courier New', monospace;
            color: 
#48bb78;
            font-size: 0.9rem;
        }

        .project-input-container {
            position: relative;
        }

        .controls {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-start {
            background: linear-gradient(135deg, 
#48bb78, 
#38a169);
            color: white;
        }

        .btn-start:hover {
            background: linear-gradient(135deg, 
#38a169, 
#2f855a);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(56, 161, 105, 0.3);
        }

        .btn-stop {
            background: linear-gradient(135deg, 
#f56565, 
#e53e3e);
            color: white;
        }

        .btn-stop:hover {
            background: linear-gradient(135deg, 
#e53e3e, 
#c53030);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(229, 62, 62, 0.3);
        }

        .btn-export {
            background: linear-gradient(135deg, 
#667eea, 
#764ba2);
            color: white;
            width: 100%;
            margin-top: 1rem;
        }

        .btn-export:hover {
            background: linear-gradient(135deg, 
#764ba2, 
#667eea);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(118, 75, 162, 0.3);
        }

        .view-controls {
            display: flex;
            gap: 0.5rem;
            margin: 1rem 0;
            justify-content: center;
        }

        .view-btn {
            padding: 0.6rem 1.2rem;
            border: 2px solid 
#e2e8f0;
            border-radius: 25px;
            background: white;
            color: 
#4a5568;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .view-btn.active {
            background: linear-gradient(135deg, 
#667eea, 
#764ba2);
            color: white;
            border-color: 
#667eea;
        }

        .view-btn:hover:not(.active) {
            background: 
#f7fafc;
            border-color: 
#cbd5e0;
        }

        .session-list {
            background: 
#f7fafc;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: left;
        }

        .session-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 10px;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .session-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .session-project {
            font-weight: 600;
            color: 
#2d3748;
            flex-grow: 1;
        }

        .session-time {
            font-family: 'Courier New', monospace;
            color: 
#718096;
            margin: 0 1rem;
        }

        .session-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .session-delete, .session-edit, .session-restart {
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .session-delete {
            background: 
#fed7d7;
            color: 
#e53e3e;
        }

        .session-delete:hover {
            background: 
#feb2b2;
            transform: scale(1.1);
        }

        .session-edit {
            background: 
#bee3f8;
            color: 
#2b6cb0;
        }

        .session-edit:hover {
            background: 
#90cdf4;
            transform: scale(1.1);
        }

        .session-restart {
            background: 
#c6f6d5;
            color: 
#2f855a;
        }

        .session-restart:hover {
            background: 
#9ae6b4;
            transform: scale(1.1);
        }

        .billable-toggle {
            margin-left: 0.5rem;
            padding: 0.2rem 0.5rem;
            border: 1px solid 
#e2e8f0;
            border-radius: 12px;
            background: white;
            font-size: 0.7rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .billable-toggle.billable {
            background: 
#c6f6d5;
            color: 
#2f855a;
            border-color: 
#68d391;
        }

        .billable-toggle.non-billable {
            background: 
#fed7d7;
            color: 
#e53e3e;
            border-color: 
#fc8181;
        }

        .recent-tasks {
            background: 
#f0fff4;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 2px solid 
#c6f6d5;
        }

        .recent-tasks h3 {
            color: 
#2f855a;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .recent-task-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem 1rem;
            background: white;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid 
#e2e8f0;
        }

        .recent-task-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(47, 133, 90, 0.1);
            border-color: 
#68d391;
        }

        .recent-task-name {
            font-weight: 600;
            color: 
#2d3748;
            flex-grow: 1;
        }

        .recent-task-time {
            font-family: 'Courier New', monospace;
            color: 
#718096;
            font-size: 0.9rem;
            margin-right: 0.5rem;
        }

        .recent-task-count {
            background: 
#e6fffa;
            color: 
#234e52;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-top: 1rem;
        }

        .status.idle {
            background: 
#fef5e7;
            color: 
#d69e2e;
            border: 1px solid 
#f6e05e;
        }

        .status.running {
            background: 
#c6f6d5;
            color: 
#2f855a;
            border: 1px solid 
#68d391;
        }

        .week-view {
            background: 
#f7fafc;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: left;
        }

        .week-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .week-navigation {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .week-nav-btn {
            background: white;
            border: 2px solid 
#e2e8f0;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .week-nav-btn:hover {
            border-color: 
#667eea;
            background: 
#f0f4ff;
        }

        .week-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: 
#2d3748;
            min-width: 200px;
            text-align: center;
        }

        .week-stats {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .week-stat {
            text-align: center;
        }

        .week-stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: 
#2d3748;
            font-family: 'Courier New', monospace;
        }

        .week-stat-label {
            font-size: 0.8rem;
            color: 
#718096;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .week-days {
            display: grid;
            gap: 1rem;
        }

        .week-day {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            border: 1px solid 
#e2e8f0;
        }

        .week-day-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid 
#f1f5f9;
        }

        .week-day-name {
            font-weight: 600;
            color: 
#2d3748;
        }

        .week-day-total {
            font-family: 'Courier New', monospace;
            color: 
#4a5568;
            font-size: 0.9rem;
        }

        .week-day-sessions {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .week-session {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            background: 
#f8f9fa;
            border-radius: 6px;
            font-size: 0.85rem;
        }

        .week-session-project {
            flex-grow: 1;
            color: 
#2d3748;
        }

        .week-session-time {
            font-family: 'Courier New', monospace;
            color: 
#718096;
            margin-right: 0.5rem;
        }

        .week-session-actions {
            display: flex;
            gap: 0.25rem;
        }

        .week-session-btn {
            border: none;
            border-radius: 4px;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 0.7rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .bulk-actions {
            background: 
#fff3cd;
            border: 1px solid 
#ffeaa7;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: none;
        }

        .bulk-actions.show {
            display: block;
        }

        .bulk-actions-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .bulk-selected-count {
            font-weight: 600;
            color: 
#856404;
        }

        .bulk-clear {
            background: none;
            border: none;
            color: 
#6c757d;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .bulk-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .bulk-btn {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .bulk-delete {
            background: 
#f8d7da;
            color: 
#721c24;
        }

        .bulk-delete:hover {
            background: 
#f5c6cb;
        }

        .bulk-export {
            background: 
#d1ecf1;
            color: 
#0c5460;
        }

        .bulk-export:hover {
            background: 
#bee5eb;
        }

        .session-checkbox {
            margin-right: 0.5rem;
            cursor: pointer;
        }

        .edit-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .edit-modal.show {
            display: flex;
        }

        .edit-modal-content {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            animation: slideIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .edit-modal h3 {
            margin-bottom: 1rem;
            color: 
#2d3748;
        }

        .edit-modal input, .edit-modal select {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid 
#e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            margin-bottom: 1rem;
        }

        .edit-modal input:focus, .edit-modal select:focus {
            outline: none;
            border-color: 
#667eea;
        }

        .edit-modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        .btn-small {
            padding: 0.6rem 1.2rem;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-cancel {
            background: 
#e2e8f0;
            color: 
#4a5568;
        }

        .btn-save {
            background: 
#667eea;
            color: white;
        }

        .client-rates-list {
            max-height: 200px;
            overflow-y: auto;
            margin: 1rem 0;
            border: 1px solid 
#e2e8f0;
            border-radius: 8px;
            background: 
#f8f9fa;
        }

        .client-rate-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            border-bottom: 1px solid 
#e2e8f0;
        }

        .client-rate-item:last-child {
            border-bottom: none;
        }

        .client-rate-name {
            flex-grow: 1;
            font-weight: 600;
        }

        .client-rate-value {
            font-family: 'Courier New', monospace;
            margin: 0 0.5rem;
        }

        .client-rate-delete {
            background: 
#fed7d7;
            color: 
#e53e3e;
            border: none;
            border-radius: 4px;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 0.7rem;
        }

        .export-options {
            margin: 1rem 0;
        }

        .export-option {
            margin-bottom: 1rem;
        }

        .export-option label {
            display: block;
            font-size: 0.9rem;
            color: 
#4a5568;
            margin-bottom: 0.3rem;
        }

        .export-option select,
        .export-option input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid 
#e2e8f0;
            border-radius: 6px;
        }

        .checkbox-group {
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-top: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 0.3rem;
        }

        .keyboard-hint {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            opacity: 0.7;
        }

        .floating-timer {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(72, 187, 120, 0.95);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-family: 'Courier New', monospace;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .floating-timer:hover {
            transform: scale(1.05);
            background: rgba(56, 161, 105, 0.95);
        }

        @media (max-width: 600px) {
            .container {
                margin: 1rem;
                padding: 1.5rem;
            }

            .timer-display {
                font-size: 2.5rem;
            }

            .controls {
                flex-direction: column;
            }

            .floating-timer {
                top: 10px;
                right: 10px;
                font-size: 0.9rem;
            }

            .week-header {
                flex-direction: column;
                gap: 1rem;
            }

            /* Fix session item layout on mobile */
            .session-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
                padding: 0.8rem;
            }

            .session-project {
                width: 100%;
                margin-bottom: 0.3rem;
            }

            .session-time {
                margin: 0;
                font-size: 1rem;
            }

            .session-actions {
                width: 100%;
                justify-content: space-between;
                margin-top: 0.5rem;
            }

            .session-delete, .session-edit, .session-restart {
                width: 35px;
                height: 35px;
                font-size: 1rem;
            }

            .billable-toggle {
                margin: 0;
                padding: 0.3rem 0.8rem;
                font-size: 0.8rem;
                align-self: flex-start;
            }

            /* Fix week session layout on mobile */
            .week-session {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.3rem;
                padding: 0.8rem;
            }

            .week-session-project {
                width: 100%;
                font-size: 0.9rem;
            }

            .week-session-time {
                margin: 0;
                font-size: 0.8rem;
            }

            .week-session-actions {
                width: 100%;
                justify-content: space-between;
                margin-top: 0.3rem;
            }

            .week-session-btn {
                width: 25px;
                height: 25px;
                font-size: 0.8rem;
            }

            /* Fix recent task layout on mobile */
            .recent-task-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.3rem;
                padding: 0.8rem;
            }

            .recent-task-name {
                width: 100%;
            }

            .recent-task-time, .recent-task-count {
                margin: 0;
            }

            /* Make modals more mobile friendly */
            .edit-modal-content {
                width: 95%;
                margin: 1rem;
            }

            /* Adjust button sizes */
            .btn {
                padding: 0.8rem 1.5rem;
                font-size: 1rem;
            }

            .btn-export {
                font-size: 0.9rem;
                padding: 0.8rem;
            }
        }

        .empty-state {
            text-align: center;
            color: 
#a0aec0;
            padding: 2rem;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QuickTrack Final</h1>
        <p class="subtitle">Complete invoice integration with client rates & smart export</p>

        <div class="timer-display" id="timerDisplay">00:00:00</div>

        <div class="project-input-container">
            <input type="text" class="project-input" id="projectInput" placeholder="What are you working on? (auto-detects from browser tab)" autocomplete="off">
            <div class="rate-indicator" id="rateIndicator" style="display: none;"></div>
            <div class="client-suggestions" id="clientSuggestions" style="display: none;"></div>
        </div>

        <div class="controls">
            <button class="btn btn-start" id="startBtn" onclick="toggleTimer()">Start</button>
        </div>

        <button class="btn btn-export" onclick="showExportModal()">📄 Export & Filter</button>

        <button class="btn btn-export" onclick="showManualEntryModal()" style="background: linear-gradient(135deg, 
#48bb78, 
#38a169); margin-top: 0.5rem;">➕ Add Manual Time</button>

        <button class="btn btn-export" onclick="showClientRatesModal()" style="background: linear-gradient(135deg, 
#667eea, 
#764ba2); margin-top: 0.5rem;">💰 Client Rates</button>

        <div class="view-controls">
            <button class="view-btn active" id="dailyViewBtn" onclick="switchView('daily')">📅 Daily</button>
            <button class="view-btn" id="weeklyViewBtn" onclick="switchView('weekly')">📊 Weekly</button>
        </div>

        <div id="status" class="status idle">Ready to track • Press Space to start/stop</div>

        <div class="recent-tasks" id="recentTasks" style="display: none;">
            <h3>🚀 Quick Start (Click to continue)</h3>
            <div id="recentTasksList"></div>
        </div>

        <div class="session-list" id="sessionList">
            <div class="empty-state">No sessions yet. Start tracking to see your sessions here!</div>
        </div>

        <div class="week-view" id="weekView" style="display: none;">
            <div class="week-header">
                <div class="week-navigation">
                    <button class="week-nav-btn" onclick="previousWeek()">‹</button>
                    <div class="week-title" id="weekTitle">This Week</div>
                    <button class="week-nav-btn" onclick="nextWeek()">›</button>
                </div>
                <button class="btn-small btn-save" onclick="toggleBulkMode()" id="bulkModeBtn">Select Multiple</button>
            </div>

            <div class="bulk-actions" id="bulkActions">
                <div class="bulk-actions-header">
                    <span class="bulk-selected-count" id="bulkSelectedCount">0 sessions selected</span>
                    <button class="bulk-clear" onclick="clearBulkSelection()">Clear all</button>
                </div>
                <div class="bulk-buttons">
                    <button class="bulk-btn bulk-delete" onclick="bulkDeleteSelected()">🗑️ Delete Selected</button>
                    <button class="bulk-btn bulk-export" onclick="bulkExportSelected()">📄 Export Selected</button>
                </div>
            </div>

            <div class="week-stats" id="weekStats">
                <div class="week-stat">
                    <div class="week-stat-value" id="weekTotalHours">0h 0m</div>
                    <div class="week-stat-label">Total Time</div>
                </div>
                <div class="week-stat">
                    <div class="week-stat-value" id="weekSessionCount">0</div>
                    <div class="week-stat-label">Sessions</div>
                </div>
                <div class="week-stat">
                    <div class="week-stat-value" id="weekProjectCount">0</div>
                    <div class="week-stat-label">Projects</div>
                </div>
                <div class="week-stat">
                    <div class="week-stat-value" id="weekAvgSession">0m</div>
                    <div class="week-stat-label">Avg Session</div>
                </div>
            </div>

            <div class="week-days" id="weekDays">
                <!-- Week days will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <div class="keyboard-hint">Space: Start/Stop • Ctrl+E: Export & Filter • ESC: Close/Focus</div>

    <div class="floating-timer" id="floatingTimer" style="display: none;" onclick="focusApp()">
        00:00:00
    </div>

    <!-- Edit Modal -->
    <div class="edit-modal" id="editModal">
        <div class="edit-modal-content">
            <h3>Edit Session</h3>
            <input type="text" id="editProjectName" placeholder="Project name...">
            <div class="edit-modal-actions">
                <button class="btn-small btn-cancel" onclick="closeEditModal()">Cancel</button>
                <button class="btn-small btn-save" onclick="saveEdit()">Save</button>
            </div>
        </div>
    </div>

    <!-- Manual Entry Modal -->
    <div class="edit-modal" id="manualEntryModal">
        <div class="edit-modal-content">
            <h3>Add Manual Time Entry</h3>
            <input type="text" id="manualProjectName" placeholder="Project name..." style="margin-bottom: 1rem;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; margin-bottom: 1rem;">
                <div>
                    <label style="display: block; font-size: 0.8rem; color: 
#4a5568; margin-bottom: 0.2rem;">Hours</label>
                    <input type="number" id="manualHours" min="0" max="23" value="0" style="width: 100%;">
                </div>
                <div>
                    <label style="display: block; font-size: 0.8rem; color: 
#4a5568; margin-bottom: 0.2rem;">Minutes</label>
                    <input type="number" id="manualMinutes" min="0" max="59" value="30" style="width: 100%;">
                </div>
            </div>
            <input type="date" id="manualDate" style="width: 100%; margin-bottom: 1rem;">
            <div class="checkbox-group">
                <label style="display: flex; align-items: center; margin: 0;">
                    <input type="checkbox" id="manualBillable" checked>
                    <span>Billable</span>
                </label>
            </div>
            <div class="edit-modal-actions">
                <button class="btn-small btn-cancel" onclick="closeManualEntryModal()">Cancel</button>
                <button class="btn-small btn-save" onclick="saveManualEntry()">Add Entry</button>
            </div>
        </div>
    </div>

    <!-- Client Rates Modal -->
    <div class="edit-modal" id="clientRatesModal">
        <div class="edit-modal-content">
            <h3>Client Hourly Rates</h3>
            <div style="display: grid; grid-template-columns: 2fr 1fr auto; gap: 0.5rem; margin-bottom: 1rem;">
                <input type="text" id="newClientName" placeholder="Client/Project name...">
                <input type="number" id="newClientRate" placeholder="Rate" min="0" step="0.01">
                <button class="btn-small btn-save" onclick="addClientRate()">Add</button>
            </div>
            <div class="client-rates-list" id="clientRatesList">
                <div style="text-align: center; color: 
#a0aec0; padding: 1rem; font-style: italic;">No rates set yet</div>
            </div>
            <div class="edit-modal-actions">
                <button class="btn-small btn-cancel" onclick="closeClientRatesModal()">Close</button>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div class="edit-modal" id="exportModal">
        <div class="edit-modal-content">
            <h3>Export & Filter Sessions</h3>
            <div class="export-options">
                <div class="export-option">
                    <label>Date Range</label>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
                        <input type="date" id="exportStartDate">
                        <input type="date" id="exportEndDate">
                    </div>
                </div>
                <div class="export-option">
                    <label>Client/Project</label>
                    <select id="exportClient">
                        <option value="">All Clients</option>
                    </select>
                </div>
                <div class="export-option">
                    <label>Billable Status</label>
                    <select id="exportBillable">
                        <option value="">All Sessions</option>
                        <option value="true">Billable Only</option>
                        <option value="false">Non-Billable Only</option>
                    </select>
                </div>
                <div class="checkbox-group">
                    <label style="display: flex; align-items: center; margin: 0;">
                        <input type="checkbox" id="includeRates" checked>
                        <span>Include hourly rates & totals in export</span>
                    </label>
                </div>
            </div>
            <div class="edit-modal-actions">
                <button class="btn-small btn-cancel" onclick="closeExportModal()">Cancel</button>
                <button class="btn-small btn-save" onclick="exportFilteredData()">Export CSV</button>
            </div>
        </div>
    </div>

    <script>
        // State management
        let isRunning = false;
        let startTime = null;
        let elapsedTime = 0;
        let currentProject = '';
        let sessions = JSON.parse(localStorage.getItem('quicktrack-sessions') || '[]');
        let clientRates = JSON.parse(localStorage.getItem('quicktrack-client-rates') || '{}');
        let timerInterval = null;
        let idleTimeout = null;
        let lastActivity = Date.now();
        let isPaused = false;
        let pauseStartTime = null;
        let editingSessionIndex = -1;
        let currentView = 'daily';
        let currentWeekStart = null;
        let bulkMode = false;
        let selectedSessions = new Set();

        // DOM elements
        const timerDisplay = document.getElementById('timerDisplay');
        const projectInput = document.getElementById('projectInput');
        const startBtn = document.getElementById('startBtn');
        const status = document.getElementById('status');
        const sessionList = document.getElementById('sessionList');
        const floatingTimer = document.getElementById('floatingTimer');
        const recentTasks = document.getElementById('recentTasks');
        const recentTasksList = document.getElementById('recentTasksList');
        const editModal = document.getElementById('editModal');
        const weekView = document.getElementById('weekView');
        const bulkActions = document.getElementById('bulkActions');
        const manualEntryModal = document.getElementById('manualEntryModal');
        const clientRatesModal = document.getElementById('clientRatesModal');
        const exportModal = document.getElementById('exportModal');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadSessions();
            loadRecentTasks();
            autoDetectProject();
            setupActivityTracking();
            initializeWeekView();
            loadClientRates();
            setupClientSuggestions();

            // Auto-save every 10 seconds when running
            setInterval(() => {
                if (isRunning) {
                    saveSessions();
                }
            }, 10000);

            // Close modal when clicking outside
            editModal.addEventListener('click', function(e) {
                if (e.target === editModal) {
                    closeEditModal();
                }
            });

            manualEntryModal.addEventListener('click', function(e) {
                if (e.target === manualEntryModal) {
                    closeManualEntryModal();
                }
            });

            clientRatesModal.addEventListener('click', function(e) {
                if (e.target === clientRatesModal) {
                    closeClientRatesModal();
                }
            });

            exportModal.addEventListener('click', function(e) {
                if (e.target === exportModal) {
                    closeExportModal();
                }
            });

            // Close suggestions when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.project-input-container')) {
                    hideClientSuggestions();
                }
            });
        });

        // Client suggestions and rate linking
        function setupClientSuggestions() {
            projectInput.addEventListener('input', function() {
                updateRateIndicator();
                showClientSuggestions();
            });

            projectInput.addEventListener('focus', function() {
                showClientSuggestions();
            });

            projectInput.addEventListener('keydown', function(e) {
                const suggestions = document.getElementById('clientSuggestions');
                const activeSuggestion = suggestions.querySelector('.client-suggestion.active');

                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    const next = activeSuggestion ? 
                        activeSuggestion.nextElementSibling : 
                        suggestions.querySelector('.client-suggestion');
                    if (next) {
                        if (activeSuggestion) activeSuggestion.classList.remove('active');
                        next.classList.add('active');
                    }
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    const prev = activeSuggestion ? 
                        activeSuggestion.previousElementSibling : 
                        suggestions.querySelector('.client-suggestion:last-child');
                    if (prev) {
                        if (activeSuggestion) activeSuggestion.classList.remove('active');
                        prev.classList.add('active');
                    }
                } else if (e.key === 'Enter' && activeSuggestion) {
                    e.preventDefault();
                    selectClient(activeSuggestion.dataset.clientName);
                } else if (e.key === 'Escape') {
                    hideClientSuggestions();
                }
            });
        }

        function updateRateIndicator() {
            const projectName = projectInput.value.trim();
            const rateIndicator = document.getElementById('rateIndicator');

            // Try exact match first
            let matchedRate = clientRates[projectName];
            let matchedClient = projectName;

            // If no exact match, try partial match
            if (!matchedRate && projectName) {
                for (const [client, rate] of Object.entries(clientRates)) {
                    if (projectName.toLowerCase().includes(client.toLowerCase()) || 
                        client.toLowerCase().includes(projectName.toLowerCase())) {
                        matchedRate = rate;
                        matchedClient = client;
                        break;
                    }
                }
            }

            if (matchedRate) {
                projectInput.classList.add('has-rate');
                rateIndicator.textContent = ${matchedRate}/hr;
                rateIndicator.style.display = 'block';
                rateIndicator.title = Rate for: ${matchedClient};
            } else {
                projectInput.classList.remove('has-rate');
                rateIndicator.style.display = 'none';
            }
        }

        function showClientSuggestions() {
            const projectName = projectInput.value.trim().toLowerCase();
            const suggestions = document.getElementById('clientSuggestions');

            if (Object.keys(clientRates).length === 0) {
                hideClientSuggestions();
                return;
            }

            // Filter clients that match the input
            const matchingClients = Object.entries(clientRates).filter(([client, rate]) => {
                if (!projectName) return true; // Show all if empty
                return client.toLowerCase().includes(projectName) || 
                       projectName.includes(client.toLowerCase());
            });

            if (matchingClients.length === 0) {
                hideClientSuggestions();
                return;
            }

            suggestions.innerHTML = '';
            matchingClients.forEach(([client, rate]) => {
                const suggestionDiv = document.createElement('div');
                suggestionDiv.className = 'client-suggestion';
                suggestionDiv.dataset.clientName = client;
                suggestionDiv.onclick = () => selectClient(client);

                suggestionDiv.innerHTML = 
                    <div class="suggestion-name">${client}</div>
                    <div class="suggestion-rate">${rate}/hr</div>
                ;

                suggestions.appendChild(suggestionDiv);
            });

            suggestions.style.display = 'block';
        }

        function hideClientSuggestions() {
            document.getElementById('clientSuggestions').style.display = 'none';
        }

        function selectClient(clientName) {
            projectInput.value = clientName;
            updateRateIndicator();
            hideClientSuggestions();
            projectInput.focus();
        }

        // Enhanced rate matching for export
        function getClientRate(projectName) {
            // Try exact match first
            if (clientRates[projectName]) {
                return clientRates[projectName];
            }

            // Try partial match
            for (const [client, rate] of Object.entries(clientRates)) {
                if (projectName.toLowerCase().includes(client.toLowerCase()) || 
                    client.toLowerCase().includes(projectName.toLowerCase())) {
                    return rate;
                }
            }

            return 0;
        }

        // Auto-detect project from browser tab title
        function autoDetectProject() {
            if (!projectInput.value && document.title !== 'QuickTrack Final - Complete Invoice Integration') {
                const cleanTitle = document.title
                    .replace(/- QuickTrack./, '')
                    .replace(/\|./, '')
                    .replace(/\s+/g, ' ')
                    .trim();
                if (cleanTitle && cleanTitle.length > 3) {
                    projectInput.value = cleanTitle;
                    updateRateIndicator();
                }
            }
        }

        // Timer functions
        function toggleTimer() {
            if (!isRunning) {
                startTimer();
            } else {
                stopTimer();
            }
        }

        function startTimer() {
            if (!isRunning) {
                currentProject = projectInput.value || 'Untitled Project';
                startTime = Date.now() - elapsedTime;
                isRunning = true;
                isPaused = false;

                startBtn.textContent = 'Stop';
                startBtn.className = 'btn btn-stop';
                timerDisplay.classList.add('running');
                status.textContent = Tracking: ${currentProject};
                status.className = 'status running';
                floatingTimer.style.display = 'block';

                timerInterval = setInterval(updateDisplay, 100);
                resetIdleTimeout();

                // Update page title
                document.title = ⏱️ ${currentProject} - QuickTrack;
            }
        }

        function stopTimer() {
            if (isRunning) {
                isRunning = false;
                clearInterval(timerInterval);
                clearTimeout(idleTimeout);

                // Save session
                const sessionDuration = Math.floor(elapsedTime / 1000);
                if (sessionDuration >= 1) { // Only save if at least 1 second
                    sessions.unshift({
                        project: currentProject,
                        duration: sessionDuration,
                        date: new Date().toISOString(),
                        startTime: new Date(startTime).toLocaleString(),
                        endTime: new Date().toLocaleString(),
                        billable: true // Default to billable
                    });
                    saveSessions();
                    loadSessions();
                    loadRecentTasks();

                    // Refresh week view if active
                    if (currentView === 'weekly') {
                        updateWeekDisplay();
                    }
                }

                // Reset UI
                elapsedTime = 0;
                startBtn.textContent = 'Start';
                startBtn.className = 'btn btn-start';
                timerDisplay.classList.remove('running');
                timerDisplay.textContent = '00:00:00';
                status.textContent = 'Ready to track • Press Space to start/stop';
                status.className = 'status idle';
                floatingTimer.style.display = 'none';

                document.title = 'QuickTrack Final - Complete Invoice Integration';

                // Clear project for next session
                projectInput.value = '';
                setTimeout(autoDetectProject, 100);
            }
        }

        function pauseTimer() {
            if (isRunning && !isPaused) {
                isPaused = true;
                pauseStartTime = Date.now();
                status.textContent = Paused: ${currentProject} (away for ${formatDuration(0)});
                status.className = 'status idle';
            }
        }

        function resumeTimer() {
            if (isRunning && isPaused) {
                isPaused = false;
                // Adjust start time to account for pause duration
                const pauseDuration = Date.now() - pauseStartTime;
                startTime += pauseDuration;
                status.textContent = Tracking: ${currentProject};
                status.className = 'status running';
                resetIdleTimeout();
            }
        }

        function updateDisplay() {
            if (!isRunning) return;

            elapsedTime = Date.now() - startTime;
            const timeString = formatDuration(elapsedTime);
            timerDisplay.textContent = timeString;
            floatingTimer.textContent = timeString;

            if (isPaused) {
                const pausedTime = Date.now() - pauseStartTime;
                status.textContent = Paused: ${currentProject} (away for ${formatDuration(pausedTime)});
            }
        }

        function formatDuration(ms) {
            const seconds = Math.floor(ms / 1000);
            const hrs = Math.floor(seconds / 3600);
            const mins = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            return ${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')};
        }

        function formatDurationShort(ms) {
            const seconds = Math.floor(ms / 1000);
            const hrs = Math.floor(seconds / 3600);
            const mins = Math.floor((seconds % 3600) / 60);

            if (hrs > 0) {
                return ${hrs}h ${mins}m;
            } else if (mins > 0) {
                return ${mins}m;
            } else {
                return ${seconds}s;
            }
        }

        // Activity tracking for idle detection
        function setupActivityTracking() {
            const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
            events.forEach(event => {
                document.addEventListener(event, () => {
                    lastActivity = Date.now();
                    if (isPaused) {
                        resumeTimer();
                    }
                    resetIdleTimeout();
                }, true);
            });
        }

        function resetIdleTimeout() {
            clearTimeout(idleTimeout);
            if (isRunning && !isPaused) {
                idleTimeout = setTimeout(() => {
                    pauseTimer();
                }, 300000); // 5 minutes idle = auto pause
            }
        }

        // Session management
        function loadSessions() {
            sessionList.innerHTML = '';

            if (sessions.length === 0) {
                sessionList.innerHTML = '<div class="empty-state">No sessions yet. Start tracking to see your sessions here!</div>';
                return;
            }

            // Group sessions by date
            const groupedSessions = {};
            sessions.forEach((session, index) => {
                const date = new Date(session.date).toDateString();
                if (!groupedSessions[date]) {
                    groupedSessions[date] = [];
                }
                groupedSessions[date].push({...session, originalIndex: index});
            });

            // Display grouped sessions
            Object.keys(groupedSessions).forEach(date => {
                const dateHeader = document.createElement('h3');
                dateHeader.textContent = date;
                dateHeader.style.color = '
#4a5568';
                dateHeader.style.marginTop = '1.5rem';
                dateHeader.style.marginBottom = '0.5rem';
                dateHeader.style.fontSize = '1rem';
                sessionList.appendChild(dateHeader);

                const dayTotal = groupedSessions[date].reduce((sum, session) => sum + session.duration, 0);
                const totalDiv = document.createElement('div');
                totalDiv.textContent = Total: ${formatDuration(dayTotal * 1000)};
                totalDiv.style.color = '
#718096';
                totalDiv.style.fontSize = '0.9rem';
                totalDiv.style.marginBottom = '1rem';
                totalDiv.style.fontWeight = '600';
                sessionList.appendChild(totalDiv);

                groupedSessions[date].forEach((session) => {
                    const sessionItem = document.createElement('div');
                    sessionItem.className = 'session-item';
                    const billableStatus = session.billable !== false ? 'billable' : 'non-billable';
                    const billableText = session.billable !== false ? 'Bill' : 'Non-Bill';

                    sessionItem.innerHTML = 
                        <div class="session-project">${session.project}</div>
                        <div class="session-time">${formatDuration(session.duration * 1000)}</div>
                        <button class="billable-toggle ${billableStatus}" onclick="toggleBillable(${session.originalIndex})">${billableText}</button>
                        <div class="session-actions">
                            <button class="session-restart" onclick="restartSession(${session.originalIndex})" title="Continue this task">
                                ▶
                            </button>
                            <button class="session-edit" onclick="editSession(${session.originalIndex})" title="Edit session">
                                ✏
                            </button>
                            <button class="session-delete" onclick="deleteSession(${session.originalIndex})" title="Delete session">
                                ×
                            </button>
                        </div>
                    ;
                    sessionList.appendChild(sessionItem);
                });
            });
        }

        function deleteSession(sessionIndex) {
            if (confirm('Delete this session?')) {
                sessions.splice(sessionIndex, 1);
                saveSessions();
                loadSessions();
                loadRecentTasks();

                // Refresh week view if active
                if (currentView === 'weekly') {
                    updateWeekDisplay();
                }
            }
        }

        function editSession(sessionIndex) {
            editingSessionIndex = sessionIndex;
            const session = sessions[sessionIndex];
            document.getElementById('editProjectName').value = session.project;
            editModal.classList.add('show');
            document.getElementById('editProjectName').focus();
        }

        function closeEditModal() {
            editModal.classList.remove('show');
            editingSessionIndex = -1;
        }

        function saveEdit() {
            if (editingSessionIndex >= 0) {
                const newProjectName = document.getElementById('editProjectName').value.trim();
                if (newProjectName) {
                    sessions[editingSessionIndex].project = newProjectName;
                    saveSessions();
                    loadSessions();
                    loadRecentTasks();

                    // Refresh week view if active
                    if (currentView === 'weekly') {
                        updateWeekDisplay();
                    }
                }
            }
            closeEditModal();
        }

        function restartSession(sessionIndex) {
            if (isRunning) {
                if (!confirm('Stop current timer and start new one?')) {
                    return;
                }
                stopTimer();
            }

            const session = sessions[sessionIndex];
            projectInput.value = session.project;
            updateRateIndicator();
            startTimer();
        }

        // Billable toggle functionality
        function toggleBillable(sessionIndex) {
            sessions[sessionIndex].billable = !sessions[sessionIndex].billable;
            saveSessions();
            loadSessions();

            // Refresh week view if active
            if (currentView === 'weekly') {
                updateWeekDisplay();
            }
        }

        // Recent tasks functionality
        function loadRecentTasks() {
            if (sessions.length === 0) {
                recentTasks.style.display = 'none';
                return;
            }

            // Get unique project names with their total time and session count
            const projectStats = {};
            sessions.forEach(session => {
                const project = session.project;
                if (!projectStats[project]) {
                    projectStats[project] = {
                        totalTime: 0,
                        sessionCount: 0,
                        lastUsed: new Date(session.date)
                    };
                }
                projectStats[project].totalTime += session.duration;
                projectStats[project].sessionCount++;

                const sessionDate = new Date(session.date);
                if (sessionDate > projectStats[project].lastUsed) {
                    projectStats[project].lastUsed = sessionDate;
                }
            });

            // Sort by last used (most recent first)
            const sortedProjects = Object.keys(projectStats)
                .sort((a, b) => projectStats[b].lastUsed - projectStats[a].lastUsed)
                .slice(0, 5); // Show top 5 recent projects

            if (sortedProjects.length === 0) {
                recentTasks.style.display = 'none';
                return;
            }

            recentTasks.style.display = 'block';
            recentTasksList.innerHTML = '';

            sortedProjects.forEach(project => {
                const stats = projectStats[project];
                const taskItem = document.createElement('div');
                taskItem.className = 'recent-task-item';
                taskItem.onclick = () => startRecentTask(project);

                taskItem.innerHTML = 
                    <div class="recent-task-name">${project}</div>
                    <div class="recent-task-time">${formatDuration(stats.totalTime * 1000)}</div>
                    <div class="recent-task-count">${stats.sessionCount}</div>
                ;

                recentTasksList.appendChild(taskItem);
            });
        }

        function startRecentTask(projectName) {
            if (isRunning) {
                if (!confirm('Stop current timer and start new one?')) {
                    return;
                }
                stopTimer();
            }

            projectInput.value = projectName;
            updateRateIndicator();
            startTimer();
        }

        function saveSessions() {
            localStorage.setItem('quicktrack-sessions', JSON.stringify(sessions));
        }

        // Manual time entry functionality
        function showManualEntryModal() {
            document.getElementById('manualProjectName').value = '';
            document.getElementById('manualHours').value = '0';
            document.getElementById('manualMinutes').value = '30';
            document.getElementById('manualDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('manualBillable').checked = true;
            manualEntryModal.classList.add('show');
            document.getElementById('manualProjectName').focus();
        }

        function closeManualEntryModal() {
            manualEntryModal.classList.remove('show');
        }

        function saveManualEntry() {
            const project = document.getElementById('manualProjectName').value.trim();
            const hours = parseInt(document.getElementById('manualHours').value) || 0;
            const minutes = parseInt(document.getElementById('manualMinutes').value) || 0;
            const dateStr = document.getElementById('manualDate').value;
            const billable = document.getElementById('manualBillable').checked;

            if (!project) {
                alert('Please enter a project name');
                return;
            }

            if (hours === 0 && minutes === 0) {
                alert('Please enter a duration greater than 0');
                return;
            }

            const totalSeconds = (hours * 3600) + (minutes * 60);
            const selectedDate = new Date(dateStr + 'T12:00:00'); // Set to noon to avoid timezone issues

            const session = {
                project: project,
                duration: totalSeconds,
                date: selectedDate.toISOString(),
                startTime: 'Manual Entry',
                endTime: 'Manual Entry',
                billable: billable
            };

            sessions.unshift(session);
            saveSessions();
            loadSessions();
            loadRecentTasks();

            // Refresh week view if active
            if (currentView === 'weekly') {
                updateWeekDisplay();
            }

            closeManualEntryModal();
        }

        // Client rates functionality
        function showClientRatesModal() {
            loadClientRates();
            clientRatesModal.classList.add('show');
            document.getElementById('newClientName').focus();
        }

        function closeClientRatesModal() {
            clientRatesModal.classList.remove('show');
        }

        function loadClientRates() {
            const ratesList = document.getElementById('clientRatesList');
            ratesList.innerHTML = '';

            if (Object.keys(clientRates).length === 0) {
                ratesList.innerHTML = '<div style="text-align: center; color: 
#a0aec0; padding: 1rem; font-style: italic;">No rates set yet</div>';
                return;
            }

            Object.entries(clientRates).forEach(([client, rate]) => {
                const rateItem = document.createElement('div');
                rateItem.className = 'client-rate-item';
                rateItem.innerHTML = 
                    <div class="client-rate-name">${client}</div>
                    <div class="client-rate-value">$${rate}/hr</div>
                    <button class="client-rate-delete" onclick="deleteClientRate('${client}')">×</button>
                ;
                ratesList.appendChild(rateItem);
            });
        }

        function addClientRate() {
            const clientName = document.getElementById('newClientName').value.trim();
            const clientRate = parseFloat(document.getElementById('newClientRate').value);

            if (!clientName) {
                alert('Please enter a client name');
                return;
            }

            if (!clientRate || clientRate <= 0) {
                alert('Please enter a valid hourly rate');
                return;
            }

            clientRates[clientName] = clientRate;
            localStorage.setItem('quicktrack-client-rates', JSON.stringify(clientRates));

            document.getElementById('newClientName').value = '';
            document.getElementById('newClientRate').value = '';

            loadClientRates();
        }

        function deleteClientRate(clientName) {
            if (confirm(Delete rate for ${clientName}?)) {
                delete clientRates[clientName];
                localStorage.setItem('quicktrack-client-rates', JSON.stringify(clientRates));
                loadClientRates();
            }
        }

        // Export functionality
        function showExportModal() {
            // Set default dates
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30); // Last 30 days

            document.getElementById('exportStartDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('exportEndDate').value = endDate.toISOString().split('T')[0];

            // Populate client dropdown
            const clientSelect = document.getElementById('exportClient');
            clientSelect.innerHTML = '<option value="">All Clients</option>';

            const uniqueClients = [...new Set(sessions.map(s => s.project))].sort();
            uniqueClients.forEach(client => {
                const option = document.createElement('option');
                option.value = client;
                option.textContent = client;
                clientSelect.appendChild(option);
            });

            exportModal.classList.add('show');
        }

        function closeExportModal() {
            exportModal.classList.remove('show');
        }

        function exportFilteredData() {
            const startDate = new Date(document.getElementById('exportStartDate').value);
            const endDate = new Date(document.getElementById('exportEndDate').value + 'T23:59:59');
            const selectedClient = document.getElementById('exportClient').value;
            const billableFilter = document.getElementById('exportBillable').value;
            const includeRates = document.getElementById('includeRates').checked;

            // Filter sessions
            let filteredSessions = sessions.filter(session => {
                const sessionDate = new Date(session.date);

                // Date filter
                if (sessionDate < startDate || sessionDate > endDate) {
                    return false;
                }

                // Client filter
                if (selectedClient && session.project !== selectedClient) {
                    return false;
                }

                // Billable filter
                if (billableFilter !== '') {
                    const isBillable = session.billable !== false;
                    if (billableFilter === 'true' && !isBillable) return false;
                    if (billableFilter === 'false' && isBillable) return false;
                }

                return true;
            });

            if (filteredSessions.length === 0) {
                alert('No sessions match the selected criteria');
                return;
            }

            const csv = generateEnhancedCSV(filteredSessions, includeRates);
            const filename = quicktrack-export-${selectedClient ? selectedClient.replace(/[^a-zA-Z0-9]/g, '-') + '-' : ''}${new Date().toISOString().split('T')[0]}.csv;
            downloadCSV(csv, filename);

            closeExportModal();
        }

        function generateEnhancedCSV(sessionsData, includeRates) {
            const headers = ['Project', 'Date', 'Start Time', 'End Time', 'Duration (Hours)', 'Duration (Minutes)', 'Billable'];

            if (includeRates) {
                headers.push('Hourly Rate', 'Total Amount');
            }

            const rows = sessionsData.map(session => {
                const billableText = session.billable !== false ? 'Yes' : 'No';
                const durationHours = (session.duration / 3600).toFixed(2);
                const durationMinutes = (session.duration / 60).toFixed(2);

                const row = [
                    session.project,
                    new Date(session.date).toLocaleDateString(),
                    session.startTime,
                    session.endTime,
                    durationHours,
                    durationMinutes,
                    billableText
                ];

                if (includeRates) {
                    const hourlyRate = getClientRate(session.project);
                    const totalAmount = session.billable !== false ? (hourlyRate * parseFloat(durationHours)).toFixed(2) : '0.00';
                    row.push(${hourlyRate}, ${totalAmount});
                }

                return row;
            });

            return [headers, ...rows].map(row => 
                row.map(cell => "${cell}").join(',')
            ).join('\n');
        }

        function downloadCSV(csv, filename) {
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // Week view functionality
        function initializeWeekView() {
            currentWeekStart = getWeekStart(new Date());
            updateWeekDisplay();
        }

        function getWeekStart(date) {
            const d = new Date(date);
            const day = d.getDay();
            // Adjust for Monday start (day 0 = Sunday, we want Monday = 0)
            const diff = d.getDate() - day + (day === 0 ? -6 : 1);
            const monday = new Date(d.setDate(diff));
            monday.setHours(0, 0, 0, 0); // Reset to start of day
            return monday;
        }

        function switchView(view) {
            currentView = view;

            // Update button states
            document.getElementById('dailyViewBtn').classList.toggle('active', view === 'daily');
            document.getElementById('weeklyViewBtn').classList.toggle('active', view === 'weekly');

            // Show/hide views
            sessionList.style.display = view === 'daily' ? 'block' : 'none';
            weekView.style.display = view === 'weekly' ? 'block' : 'none';

            if (view === 'weekly') {
                updateWeekDisplay();
            }
        }

        function previousWeek() {
            currentWeekStart.setDate(currentWeekStart.getDate() - 7);
            updateWeekDisplay();
        }

        function nextWeek() {
            currentWeekStart.setDate(currentWeekStart.getDate() + 7);
            updateWeekDisplay();
        }

        function updateWeekDisplay() {
            const weekEnd = new Date(currentWeekStart);
            weekEnd.setDate(weekEnd.getDate() + 6);

            const isCurrentWeek = isThisWeek(currentWeekStart);
            const weekTitle = isCurrentWeek ? 'This Week' : 
                ${currentWeekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })};

            document.getElementById('weekTitle').textContent = weekTitle;

            loadWeekData();
        }

        function isThisWeek(date) {
            const today = new Date();
            const thisWeekStart = getWeekStart(today);
            return date.toDateString() === thisWeekStart.toDateString();
        }

        function loadWeekData() {
            const weekEnd = new Date(currentWeekStart);
            weekEnd.setDate(weekEnd.getDate() + 6);

            // Filter sessions for this week
            const weekSessions = sessions.filter(session => {
                const sessionDate = new Date(session.date);
                return sessionDate >= currentWeekStart && sessionDate <= weekEnd;
            });

            // Calculate week stats
            const totalSeconds = weekSessions.reduce((sum, session) => sum + session.duration, 0);
            const sessionCount = weekSessions.length;
            const uniqueProjects = new Set(weekSessions.map(s => s.project)).size;
            const avgSession = sessionCount > 0 ? totalSeconds / sessionCount : 0;

            // Update stats display
            document.getElementById('weekTotalHours').textContent = formatDurationShort(totalSeconds * 1000);
            document.getElementById('weekSessionCount').textContent = sessionCount;
            document.getElementById('weekProjectCount').textContent = uniqueProjects;
            document.getElementById('weekAvgSession').textContent = formatDurationShort(avgSession * 1000);

            // Load week days
            loadWeekDays(weekSessions);
        }

        function loadWeekDays(weekSessions) {
            const weekDays = document.getElementById('weekDays');
            weekDays.innerHTML = '';

            const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

            for (let i = 0; i < 7; i++) {
                const dayDate = new Date(currentWeekStart);
                dayDate.setDate(dayDate.getDate() + i);

                const daySessions = weekSessions.filter(session => {
                    const sessionDate = new Date(session.date);
                    return sessionDate.toDateString() === dayDate.toDateString();
                });

                const dayTotal = daySessions.reduce((sum, session) => sum + session.duration, 0);

                const dayDiv = document.createElement('div');
                dayDiv.className = 'week-day';

                dayDiv.innerHTML = 
                    <div class="week-day-header">
                        <div class="week-day-name">${dayNames[i]} ${dayDate.getDate()}</div>
                        <div class="week-day-total">${formatDurationShort(dayTotal * 1000)}</div>
                    </div>
                    <div class="week-day-sessions">
                        ${daySessions.length === 0 ? '<div style="color: #a0aec0; font-style: italic; text-align: center; padding: 1rem;">No sessions</div>' : 
                          daySessions.map((session, index) => {
                            const sessionIndex = sessions.indexOf(session);
                            const billableStatus = session.billable !== false ? 'billable' : 'non-billable';
                            const billableText = session.billable !== false ? 'B' : 'NB';
                            return 
                                <div class="week-session">
                                    ${bulkMode ? <input type="checkbox" class="session-checkbox" data-session-index="${sessionIndex}" onchange="toggleSessionSelection(${sessionIndex})"> : ''}
                                    <div class="week-session-project">${session.project}</div>
                                    <div class="week-session-time">${formatDurationShort(session.duration * 1000)}</div>
                                    <button class="billable-toggle ${billableStatus}" onclick="toggleBillable(${sessionIndex})" style="font-size: 0.6rem; padding: 0.1rem 0.3rem; margin-right: 0.3rem;">${billableText}</button>
                                    <div class="week-session-actions">
                                        <button class="week-session-btn session-restart" onclick="restartSession(${sessionIndex})" title="Continue" style="background: 
#c6f6d5; color: 
#2f855a;">▶</button>
                                        <button class="week-session-btn session-edit" onclick="editSession(${sessionIndex})" title="Edit" style="background: 
#bee3f8; color: 
#2b6cb0;">✏</button>
                                        <button class="week-session-btn session-delete" onclick="deleteSession(${sessionIndex})" title="Delete" style="background: 
#fed7d7; color: 
#e53e3e;">×</button>
                                    </div>
                                </div>
                            ;
                          }).join('')
                        }
                    </div>
                ;

                weekDays.appendChild(dayDiv);
            }
        }

        // Bulk operations
        function toggleBulkMode() {
            bulkMode = !bulkMode;
            const btn = document.getElementById('bulkModeBtn');
            btn.textContent = bulkMode ? 'Exit Select Mode' : 'Select Multiple';

            if (!bulkMode) {
                clearBulkSelection();
            }

            if (currentView === 'weekly') {
                loadWeekData(); // Refresh to show/hide checkboxes
            }
        }

        function toggleSessionSelection(sessionIndex) {
            if (selectedSessions.has(sessionIndex)) {
                selectedSessions.delete(sessionIndex);
            } else {
                selectedSessions.add(sessionIndex);
            }

            updateBulkActions();
        }

        function updateBulkActions() {
            const count = selectedSessions.size;
            document.getElementById('bulkSelectedCount').textContent = ${count} session${count !== 1 ? 's' : ''} selected;
            bulkActions.classList.toggle('show', count > 0);
        }

        function clearBulkSelection() {
            selectedSessions.clear();
            updateBulkActions();

            // Uncheck all checkboxes
            document.querySelectorAll('.session-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        function bulkDeleteSelected() {
            if (selectedSessions.size === 0) return;

            if (confirm(Delete ${selectedSessions.size} selected session${selectedSessions.size !== 1 ? 's' : ''}?)) {
                // Sort indices in descending order to delete from end to beginning
                const sortedIndices = Array.from(selectedSessions).sort((a, b) => b - a);

                sortedIndices.forEach(index => {
                    sessions.splice(index, 1);
                });

                saveSessions();
                loadSessions();
                loadRecentTasks();
                clearBulkSelection();

                if (currentView === 'weekly') {
                    updateWeekDisplay();
                }
            }
        }

        function bulkExportSelected() {
            if (selectedSessions.size === 0) return;

            const selectedSessionData = Array.from(selectedSessions).map(index => sessions[index]);
            const csv = generateEnhancedCSV(selectedSessionData, true);
            downloadCSV(csv, quicktrack-selected-${new Date().toISOString().split('T')[0]}.csv);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Space to toggle timer (only if not in input field)
            if (e.code === 'Space' && e.target !== projectInput && e.target !== document.getElementById('editProjectName') && e.target !== document.getElementById('manualProjectName') && e.target !== document.getElementById('newClientName')) {
                e.preventDefault();
                toggleTimer();
            }

            // Ctrl+E to export
            if (e.ctrlKey && e.key === 'e') {
                e.preventDefault();
                showExportModal();
            }

            // Escape to focus project input or close modal
            if (e.key === 'Escape') {
                if (editModal.classList.contains('show')) {
                    closeEditModal();
                } else if (manualEntryModal.classList.contains('show')) {
                    closeManualEntryModal();
                } else if (clientRatesModal.classList.contains('show')) {
                    closeClientRatesModal();
                } else if (exportModal.classList.contains('show')) {
                    closeExportModal();
                } else {
                    projectInput.focus();
                }
            }

            // Enter to save edit in modal
            if (e.key === 'Enter' && editModal.classList.contains('show')) {
                e.preventDefault();
                saveEdit();
            }

            // Enter to save manual entry in modal
            if (e.key === 'Enter' && manualEntryModal.classList.contains('show')) {
                e.preventDefault();
                saveManualEntry();
            }

            // Enter to add client rate
            if (e.key === 'Enter' && clientRatesModal.classList.contains('show')) {
                e.preventDefault();
                addClientRate();
            }

            // Enter to export filtered data
            if (e.key === 'Enter' && exportModal.classList.contains('show')) {
                e.preventDefault();
                exportFilteredData();
            }
        });

        // Focus app when clicking floating timer
        function focusApp() {
            window.scrollTo(0, 0);
            timerDisplay.scrollIntoView({ behavior: 'smooth' });
        }

        // Auto-detect project changes
        projectInput.addEventListener('blur', function() {
            autoDetectProject();
            updateRateIndicator();
        });

        // Prevent accidental navigation
        window.addEventListener('beforeunload', function(e) {
            if (isRunning) {
                e.preventDefault();
                e.returnValue = '';
                return 'You have a timer running. Are you sure you want to leave?';
            }
        });

        // Browser visibility API for pause/resume
        document.addEventListener('visibilitychange', function() {
            if (document.hidden && isRunning) {
                // Don't auto-pause just because tab is hidden - many freelancers work with multiple tabs
                // But track that we might be away
                setTimeout(() => {
                    if (document.hidden && Date.now() - lastActivity > 600000) { // 10 minutes
                        pauseTimer();
                    }
                }, 600000);
            }
        });
    </script>
</body>
</html>